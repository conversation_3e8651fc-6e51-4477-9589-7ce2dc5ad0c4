// flash-usdt-nile-proxy.js
import express from 'express';
import fetch from 'node-fetch';
import bodyParser from 'body-parser';
import TronWeb from 'tronweb';

const app = express();
app.use(bodyParser.json());

const REAL_RPC     = 'https://api.nile.trongrid.io';
const USDT_ADDRESS = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';
const TARGET_WALLET = 'TCpjaNXeKC8bErbbC1zFqPPN2abWJYwXjH'; // 🎯 محفظتك
const FAKE_AMOUNT   = BigInt(10_000_000); // 10 USDT = 10_000_000 (6 decimals)
const FLASH_DURATION = 90 * 24 * 3600 * 1000; // 90 يوم

const tronWeb = new TronWeb({ fullHost: REAL_RPC });
let flashBook = {};

(async () => {
  const hex = tronWeb.address.toHex(TARGET_WALLET).slice(2);
  flashBook[hex] = {
    rawAmount: FAKE_AMOUNT,
    expires: Date.now() + FLASH_DURATION
  };
})();

// هل الرصيد لا يزال صالحًا؟
function isActive(userHex) {
  const e = flashBook[userHex];
  return e && Date.now() < e.expires;
}

// كل POST طلب
app.post('*', async (req, res) => {
  const { url } = req;
  let upstream, json;

  try {
    upstream = await fetch(REAL_RPC + url, {
      method: 'POST',
      headers: req.headers,
      body: JSON.stringify(req.body)
    });
    json = await upstream.json();
  } catch (err) {
    return res.status(502).json({ error: 'Upstream error', details: err.toString() });
  }

  // 📍 balanceOf(address)
  if (url.endsWith('/wallet/triggerconstantcontract') && json.constant_result) {
    const { parameter, contract_address, function_selector } = req.body;
    if (
      function_selector === 'balanceOf(address)' &&
      contract_address.toLowerCase() === tronWeb.address.toHex(USDT_ADDRESS).toLowerCase()
    ) {
      const userHex = parameter.slice(-64);
      if (isActive(userHex)) {
        const amt = flashBook[userHex].rawAmount;
        const fakeHex = amt.toString(16).padStart(64, '0');
        json.constant_result = [fakeHex];
      }
    }
    return res.json(json);
  }

  // 📍 wallet/getaccount → إضافة الرصيد ضمن assetV2
  if (url.endsWith('/wallet/getaccount')) {
    const acc = json;
    acc.assetV2 = acc.assetV2 || [];
    const user58 = req.body.address;
    const userHex = tronWeb.address.toHex(user58).slice(2);
    if (isActive(userHex)) {
      acc.assetV2 = acc.assetV2.filter(x => x.key !== USDT_ADDRESS);
      acc.assetV2.push({
        key:   USDT_ADDRESS,
        value: Number(flashBook[userHex].rawAmount)
      });
    }
    return res.json(acc);
  }

  res.json(json);
});

app.listen(9090, () => {
  console.log(`✅ Flash USDT Nile Proxy ready at http://localhost:9090`);
  console.log(`🎯 Wallet: ${TARGET_WALLET} will show 10 USDT on Nile`);
});
