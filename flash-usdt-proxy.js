const TronWeb = require('tronweb');

const USDT_CONTRACT = 'TNKTpYraUGJfnzFYnBWZXVJo4bubp8bGvw';
const RECEIVER = 'TCpjaNXeKC8bErbbC1zFqPPN2abWJYwXjH';
const SENDER = 'TCpjaNXeKC8bErbbC1zFqPPN2abWJYwXjH';  // محفظتك على Nile لإرسال التوكن (وهمية)
const PRIVATE_KEY = '328d94b8a97fec6b1d07da288ff9922294519b0c1c1e18e782f78a2c54ec8055';

const tronWeb = new TronWeb({
    fullHost: 'https://nile.trongrid.io',
    headers: { "TRON-PRO-API-KEY": "YOUR_API_KEY" },
    privateKey: PRIVATE_KEY
});

async function sendFakeUSDT() {
    const contract = await tronWeb.contract().at(USDT_CONTRACT);

    try {
        const tx = await contract.transfer(RECEIVER, 10_000_000).send({
            feeLimit: 100_000_000
        });
        console.log('تم إرسال 10 USDT وهمي إلى:', RECEIVER);
        console.log('Transaction:', tx);
    } catch (err) {
        console.error('فشل الإرسال:', err);
    }
}

sendFakeUSDT();
