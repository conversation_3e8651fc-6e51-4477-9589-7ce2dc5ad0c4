const { TronWeb } = require('tronweb');

// 🎯 إعدادات Flash USDT
const USDT_CONTRACT = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';  // عقد USDT على Nile (الصحيح)
const RECEIVER = 'TCpjaNXeKC8bErbbC1zFqPPN2abWJYwXjH';        // المحفظة المستقبلة
const SENDER = 'TCpjaNXeKC8bErbbC1zFqPPN2abWJYwXjH';          // المحفظة المرسلة
const PRIVATE_KEY = '328d94b8a97fec6b1d07da288ff9922294519b0c1c1e18e782f78a2c54ec8055';

// 💡 للحصول على TRX و USDT مجاني للاختبار:
// 1. TRX: اذهب إلى https://nileex.io/join/getJoinPage
//    أدخل عنوان محفظتك: TCpjaNXeKC8bErbbC1zFqPPN2abWJYwXjH
//    ستحصل على 10,000 TRX مجاناً
// 2. USDT: في نفس الموقع، ستحصل على 1000 USDT تجريبي
//    عنوان عقد USDT: TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf

// خوادم بديلة للاختيار من بينها:
// 'https://nile.trongrid.io'           - الخادم الرسمي (قد يحتاج API key للاستخدام المكثف)
// 'https://api.nileex.io'              - خادم بديل مجاني
// 'https://nile.tronex.io'             - خادم آخر
// 'https://api.nile.tronstack.io'      - خادم مجتمعي

const tronWeb = new TronWeb({
    fullHost: 'https://api.nileex.io',  // خادم مجاني بديل
    privateKey: PRIVATE_KEY
});
 
async function checkBalance() {
    try {
        const balance = await tronWeb.trx.getBalance(SENDER);
        const trxBalance = tronWeb.fromSun(balance);
        console.log(`💰 رصيد TRX: ${trxBalance} TRX`);

        if (trxBalance < 10) {
            console.log('⚠️  تحذير: رصيد TRX منخفض، قد تحتاج المزيد لدفع رسوم المعاملة');
        }

        return trxBalance > 0;
    } catch (err) {
        console.error('❌ خطأ في فحص الرصيد:', err.message);
        return false;
    }
}

async function checkUSDTBalance(address) {
    try {
        const contract = await tronWeb.contract().at(USDT_CONTRACT);
        const balance = await contract.balanceOf(address).call();

        // تحويل الرصيد بطرق مختلفة حسب النوع
        let usdtBalance = 0;
        if (typeof balance === 'object' && balance._hex) {
            usdtBalance = parseInt(balance._hex, 16) / 1000000;
        } else if (typeof balance === 'string') {
            usdtBalance = parseInt(balance) / 1000000;
        } else if (typeof balance === 'number') {
            usdtBalance = balance / 1000000;
        } else {
            usdtBalance = Number(balance) / 1000000;
        }

        console.log(`💵 رصيد USDT: ${usdtBalance} USDT`);
        return usdtBalance;
    } catch (err) {
        console.error('❌ خطأ في فحص رصيد USDT:', err.message);
        return 0;
    }
}

async function sendFakeUSDT() {
    console.log('🚀 بدء إرسال USDT وهمي...');
    console.log(`📤 من: ${SENDER}`);
    console.log(`📥 إلى: ${RECEIVER}`);
    console.log(`💵 المبلغ: 10 USDT`);

    // فحص الرصيد أولاً
    const hasBalance = await checkBalance();
    if (!hasBalance) {
        console.log('❌ لا يوجد رصيد TRX كافي');
        return;
    }

    // فحص رصيد USDT قبل الإرسال
    console.log('🔍 فحص رصيد USDT قبل الإرسال...');
    const usdtBefore = await checkUSDTBalance(SENDER);

    try {
        const contract = await tronWeb.contract().at(USDT_CONTRACT);
        console.log('📋 تم الاتصال بعقد USDT');

        const result = await contract.transfer(RECEIVER, 10_000_000).send({
            feeLimit: 100_000_000
            callValue: 0,
            shouldPollResponse: true
        });

        console.log('✅ تم إرسال 10 USDT وهمي بنجاح!');
        console.log('📋 تفاصيل المعاملة:', result);

        // استخراج Transaction ID
        let txId = result;
        if (typeof result === 'object' && result.txid) {
            txId = result.txid;
        } else if (typeof result === 'object' && result.transaction) {
            txId = result.transaction.txID;
        }

        if (txId && txId !== 'false') {
            console.log(`🔗 Transaction ID: ${txId}`);
            console.log(`🌐 TronScan: https://nile.tronscan.org/#/transaction/${txId}`);
        } else {
            console.log('⚠️ لم يتم الحصول على Transaction ID، لكن المعاملة قد تكون نجحت');
        }

        // فحص رصيد USDT بعد الإرسال
        console.log('🔍 فحص رصيد USDT بعد الإرسال...');
        const usdtAfter = await checkUSDTBalance(RECEIVER);

        if (usdtAfter > usdtBefore) {
            console.log(`✅ تم زيادة الرصيد بنجاح! الفرق: ${usdtAfter - usdtBefore} USDT`);
        }

    } catch (err) {
        console.error('❌ فشل الإرسال:', err.message);

        // رسائل خطأ مفيدة
        if (err.message.includes('REVERT')) {
            console.log('💡 نصيحة: تأكد من أن العقد صحيح والمحفظة تحتوي على USDT');
        } else if (err.message.includes('bandwidth')) {
            console.log('💡 نصيحة: تحتاج المزيد من TRX لدفع رسوم المعاملة');
        } else if (err.message.includes('energy')) {
            console.log('💡 نصيحة: تحتاج المزيد من Energy أو TRX');
        }
    }
}

// تشغيل الوظيفة
console.log('🔥 Flash USDT Sender - Nile Testnet');
console.log('=====================================');
sendFakeUSDT();
